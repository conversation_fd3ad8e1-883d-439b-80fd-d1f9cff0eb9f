@inject IConfiguration Configuration
@inject IJSRuntime JS
@using ApexCharts
@using System.Linq
@using System.Collections.Generic
@using System.Data
@using Dashboard_Yuntech.Models.AzureCosmos
@using Dashboard_Yuntech.Models.ChartModels
@using Newtonsoft.Json
@using System.Reflection
@using OfficeOpenXml
@using Dashboard_Yuntech.Service
@using static Dashboard_Yuntech.Service.ChartDataService
@inject AzureCosmosService CosmosService
@inject ChartDataService _chartDataService
@inject ChartConfigurationService _configService
@rendermode @(new Microsoft.AspNetCore.Components.Web.InteractiveServerRenderMode())

<div class="card-container h-100">
    <div class="card @Css h-100">
        <div class="card-header d-flex justify-content-between align-items-center mt-2">
            <div class="d-flex align-items-center gap-2">
                <span class="badge bg-secondary fw-bold fs-5">@Year</span>
                <span class="fw-bold fs-5">@Title</span>
            </div>

            @if (IsModal)
            {
                <div>
                    @* 互動視窗表格 *@
                    <BS5Modal Id="@ModalId" Title="@($"{Title} - 數據表格")">
                        <ButtonContent>
                            <i class="fa-solid fa-table"></i>
                        </ButtonContent>

                        <ChildContent>
                            <TableExcel TableColumns="@TableColumns" TableData="@TableData" />
                        </ChildContent>
                    </BS5Modal>

                    <!-- 互動視窗圖表 -->
                    <BS5Modal Id="chartModal" Title="@($"統計圖表")">
                        <ButtonContent>
                            <i class="fa-solid fa-expand"></i>
                        </ButtonContent>

                        <ChildContent>
                            @if (isLoading)
                            {
                                <div class="text-center mt-5">
                                    <div class="spinner-border mt-5" role="status">
                                    </div>
                                </div>
                            }
                            else if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger">@errorMessage</div>
                            }
                            else if (internalChartData == null || !internalChartData.Any())
                            {
                                <div class="alert alert-info">沒有可顯示的數據</div>
                            }
                            else
                            {
                                @if (shouldRenderChart)
                                {
                                    <ApexChart TItem="ChartDataItem"
                                    Options="modalChartOptions">
                                    @if (!MultiSeries)
                                    {
                                        @if (isShowDataLabels)
                                        {
                                            <ApexPointSeries TItem="ChartDataItem"
                                            Name="@SeriesName"
                                            Items="@internalChartData"
                                            XValue="@XValueSelector"
                                            YValue="@YValueSelector"
                                            SeriesType="@ChartType"
                                            ShowDataLabels />
                                        }
                                        else
                                        {
                                            <ApexPointSeries TItem="ChartDataItem"
                                            Name="@SeriesName"
                                            Items="@internalChartData"
                                            XValue="@XValueSelector"
                                            YValue="@YValueSelector"
                                            SeriesType="@ChartType" />
                                        }
                                    }
                                    else
                                    {
                                        var ySelector = CreateYValueSelector();
                                        foreach (var prop in GetValueProperties())
                                        {
                                            var seriesType = GetSeriesType(prop.PropName);

                                            @if (isShowDataLabels)
                                            {
                                                <ApexPointSeries TItem="ChartDataItem"
                                                Name="@prop.DisplayName"
                                                Items="@internalChartData"
                                                XValue="@XValueSelector"
                                                YValue="@(item => {
                                                                     var value = ySelector(item, prop.PropName);
                                                                     return value;
                                                                 })"
                                                SeriesType="@seriesType"
                                                ShowDataLabels />
                                            }
                                            else
                                            {
                                                <ApexPointSeries TItem="ChartDataItem"
                                                Name="@prop.DisplayName"
                                                Items="@internalChartData"
                                                XValue="@XValueSelector"
                                                YValue="@(item => {
                                                                     var value = ySelector(item, prop.PropName);
                                                                     return value;
                                                                 })"
                                                SeriesType="@seriesType" />
                                            }
                                        }
                                    }
                                </ApexChart>
                                }
                            }
                        </ChildContent>
                    </BS5Modal>

                    @* 下載 *@
                    @if (!string.IsNullOrEmpty(ExcelUrl))
                    {
                        <a href="@GetDownloadUrl()" class="ms-2" download>
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                    else if (OnExportExcel.HasDelegate)
                    {
                        <a href="javascript:void(0)" class="ms-2" @onclick="OnExportExcel" @onclick:preventDefault="true" title="匯出Excel">
                            <i class="fa-solid fa-download"></i>
                        </a>
                    }
                </div>
            }

        </div>
        <div class="card-body @bodyCss">
             @ChildContent
        </div>
    </div>
</div>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Year { get; set; }
    [Parameter] public string? Css { get; set; }
    [Parameter] public string? bodyCss { get; set; }
    [Parameter] public bool IsModal { get; set; } = false;
    [Parameter] public string? ExcelUrl { get; set; }
    [Parameter] public string ModalId { get; set; } = "myModal";
    [Parameter] public EventCallback OnExportExcel { get; set; }

    [Parameter] public List<string>? TableColumns { get; set; }

    /// <summary>
    /// TableData 為 Dictionary 的 List，每筆 Dictionary 表示一列資料，key 是欄位名稱
    /// </summary>
    [Parameter] public List<Dictionary<string, object>>? TableData { get; set; }

    // 圖表相關參數
    [Parameter] public ContainerType ContainerType { get; set; } = ContainerType.Res_CombineMoney;
    [Parameter] public List<ChartDataItem> ChartData { get; set; }
    [Parameter] public ApexChartOptions<ChartDataItem> ChartOptions { get; set; }
    [Parameter] public SeriesType ChartType { get; set; } = SeriesType.Bar;
    [Parameter] public string SeriesName { get; set; } = "數據";
    [Parameter] public Func<ChartDataItem, object> XValueSelector { get; set; }
    [Parameter] public Func<ChartDataItem, decimal?> YValueSelector { get; set; }
    [Parameter] public EventCallback<List<Dictionary<string, object>>> OnDataLoaded { get; set; }
    [Parameter] public bool MultiSeries { get; set; } = false;
    [Parameter] public bool isShowDataLabels { get; set; } = true;
    [Parameter] public bool IsMixedChart { get; set; } = false;
    [Parameter] public Dictionary<string, SeriesType> SeriesTypes { get; set; } = new Dictionary<string, SeriesType>();
    [Parameter] public string CategoryField { get; set; }
    [Parameter] public string ValueField { get; set; }
    [Parameter] public string CosmosDataPropertyName { get; set; }
    [Parameter] public IEnumerable<object>? PreChartData { get; set; }
    [Parameter] public TreemapConfiguration TreemapConfig { get; set; }
    [Parameter] public EventCallback<TreemapConfiguration> TreemapConfigChanged { get; set; }
    [Parameter] public bool TimeSeriesMode { get; set; } = false;
    [Parameter] public string? TargetSchoolName { get; set; }
    [Parameter] public Dictionary<string, string> PieChartFields { get; set; }

    /// <summary>
    /// 取得下載 URL，在正式環境時加上 Url 前綴
    /// </summary>
    private string GetDownloadUrl()
    {
        string apiUrl = Configuration["SysSetting:DownLoadUrl"];
        string downloadUrl = $"{apiUrl}/Excel/download?url={Uri.EscapeDataString(ExcelUrl ?? "")}";

        return downloadUrl;
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }

    // 通用數據模型
    public class ChartDataItem
    {
        public string SchoolName { get; set; } = "";
        public int AcademicYear { get; set; }
        public decimal Value { get; set; }
        public Dictionary<string, decimal> ValueColumns { get; set; } = new Dictionary<string, decimal>();

        // 動態屬性存取
        public object GetPropertyValue(string propertyName)
        {
            var property = this.GetType().GetProperty(propertyName);
            return property?.GetValue(this) ?? "";
        }

        public void SetPropertyValue(string propertyName, object value)
        {
            var property = this.GetType().GetProperty(propertyName);
            if (property != null && property.CanWrite)
            {
                var convertedValue = ConvertValue(value, property.PropertyType);
                property.SetValue(this, convertedValue);
            }
        }

        private object ConvertValue(object value, Type targetType)
        {
            if (value == null) return GetDefaultValue(targetType);
            if (targetType.IsAssignableFrom(value.GetType())) return value;

            // 處理可空類型
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                targetType = Nullable.GetUnderlyingType(targetType);
            }

            try
            {
                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                return GetDefaultValue(targetType);
            }
        }

        private object GetDefaultValue(Type type)
        {
            if (type == null) return null;
            if (type.IsValueType) return Activator.CreateInstance(type);
            return null;
        }
    }

    // 數據和變數
    private bool isLoading = true;
    private string? errorMessage;
    private List<ChartDataItem>? internalChartData;

    // 控制圖表渲染
    private bool shouldRenderChart = true;

    // 圖表選項
    private ApexChartOptions<ChartDataItem> modalChartOptions = new();

    protected override async Task OnInitializedAsync()
    {
        if (ChartData != null && ChartData.Any())
        {
            // 如果有外部提供的圖表數據，直接使用
            internalChartData = ChartData;
            isLoading = false;
        }
        else if (PreChartData != null)
        {
            // 如果有預載入數據，進行處理
            await ProcessPreChartData();
        }
        else
        {
            // 生成示例數據
            GenerateExampleData();
        }

        SetupChartOptions();
        await InvokeAsync(StateHasChanged);
    }

    private async Task ProcessPreChartData()
    {
        try
        {
            isLoading = true;

            // 這裡需要根據實際的數據類型進行處理
            // 由於是通用組件，我們需要使用反射來處理不同類型的數據
            var processedData = new List<ChartDataItem>();

            foreach (var item in PreChartData)
            {
                var chartItem = new ChartDataItem();

                // 使用反射複製屬性
                var sourceType = item.GetType();
                var properties = sourceType.GetProperties();

                foreach (var prop in properties)
                {
                    var value = prop.GetValue(item);
                    if (value != null)
                    {
                        // 嘗試設置到 ChartDataItem 的對應屬性
                        chartItem.SetPropertyValue(prop.Name, value);
                    }
                }

                processedData.Add(chartItem);
            }

            internalChartData = processedData;
            isLoading = false;
        }
        catch (Exception ex)
        {
            errorMessage = $"處理數據時發生錯誤: {ex.Message}";
            isLoading = false;
        }
    }

    private void GenerateExampleData()
    {
        var random = new Random();
        var months = new[] { "1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月" };

        internalChartData = months.Select(month => new ChartDataItem
        {
            SchoolName = month,
            Value = random.Next(50000, 200000)
        }).ToList();

        isLoading = false;
    }

    private void SetupChartOptions()
    {
        // 如果外部已提供圖表選項，則使用外部選項
        if (ChartOptions != null)
        {
            modalChartOptions = ChartOptions;
            return;
        }

        // 否則創建默認的圖表選項
        modalChartOptions = new ApexChartOptions<ChartDataItem>
        {
            Theme = new Theme { Mode = Mode.Light },
            Chart = new Chart
            {
                Toolbar = new Toolbar { Show = true },
                Background = "transparent"
            },
            Colors = new List<string> { "#e74c3c" },
            DataLabels = new DataLabels { Enabled = true },
            Xaxis = new XAxis
            {
                Title = new AxisTitle { Text = "類別" }
            },
            Yaxis = new List<YAxis>
            {
                new YAxis
                {
                    Title = new AxisTitle { Text = "數值" },
                    Labels = new YAxisLabels
                    {
                        Formatter = "function(value) { return value.toLocaleString(); }"
                    }
                }
            }
        };
    }

    // 抽出屬性讀取方法
    private List<(string PropName, string DisplayName)> GetValueProperties()
    {
        var result = new List<(string PropName, string DisplayName)>();

        var firstItem = internalChartData?.FirstOrDefault();
        if (firstItem == null) return result;

        if (firstItem.ValueColumns != null && firstItem.ValueColumns.Any())
        {
            foreach (var pair in firstItem.ValueColumns)
            {
                result.Add((pair.Key, pair.Key));
            }
        }
        else
        {
            // 使用反射獲取數值屬性
            var properties = typeof(ChartDataItem).GetProperties()
                .Where(p => p.PropertyType == typeof(decimal) && p.Name != "Value")
                .ToList();

            foreach (var prop in properties)
            {
                result.Add((prop.Name, GetDisplayName(prop.Name)));
            }
        }

        return result;
    }

    // 抽出通用 YValueSelector 方法
    private Func<ChartDataItem, string, decimal?> CreateYValueSelector()
    {
        return (item, propName) =>
        {
            if (item == null) return null;

            // 嘗試從 ValueColumns 抓取
            if (item.ValueColumns != null && item.ValueColumns.ContainsKey(propName))
            {
                return item.ValueColumns[propName];
            }

            // 再用反射抓取欄位
            var property = typeof(ChartDataItem).GetProperty(propName);
            if (property != null && property.PropertyType == typeof(decimal))
            {
                return (decimal?)property.GetValue(item);
            }

            return null;
        };
    }

    // 根據屬性名稱獲取圖表類型
    private SeriesType GetSeriesType(string propName)
    {
        // 如果是混合圖表且有對應的系列類型配置，則使用配置的類型
        if (IsMixedChart && SeriesTypes != null && SeriesTypes.ContainsKey(propName))
        {
            return SeriesTypes[propName];
        }

        // 否則使用預設圖表類型
        return ChartType;
    }

    private string GetDisplayName(string propertyName)
    {
        // 針對常見屬性名稱返回友好顯示名
        switch (propertyName)
        {
            case "SmallMoney": return "小計金額";
            case "GovMoney": return "政府部門資助";
            case "CompanyMoney": return "企業部門資助";
            case "NonProfitMoney": return "非營利機構資助";
            case "TeacherCount": return "專任教師數";
            case "StudentCount": return "學生人數";
            default: return propertyName;
        }
    }
}
